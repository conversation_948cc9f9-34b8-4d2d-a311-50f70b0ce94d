using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WatermarkCore.Models.DTOs;
using WatermarkCore.Services;

namespace WatermarkCore.Controllers
{
    /// <summary>
    /// 认证控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="loginRequest">登录请求</param>
        /// <returns>登录响应</returns>
        [HttpPost("login")]
        public async Task<ActionResult<LoginResponseDto>> Login([FromBody] LoginRequestDto loginRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _authService.LoginAsync(loginRequest);
                if (result == null)
                {
                    return Unauthorized("用户名或密码错误");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登录时发生错误");
                return StatusCode(500, "登录失败，请稍后重试");
            }
        }

        /// <summary>
        /// 用户注册
        /// </summary>
        /// <param name="registerRequest">注册请求</param>
        /// <returns>用户信息</returns>
        [HttpPost("register")]
        public async Task<ActionResult<UserDto>> Register([FromBody] RegisterRequestDto registerRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // 获取当前用户ID（如果是管理员创建子账户）
                string? createdById = null;
                if (User.Identity?.IsAuthenticated == true)
                {
                    createdById = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    
                    // 只有管理员可以指定角色
                    var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                    if (userRole != "Admin" && !string.IsNullOrEmpty(registerRequest.Role))
                    {
                        registerRequest.Role = null; // 普通用户不能指定角色
                    }
                }

                var result = await _authService.RegisterAsync(registerRequest, createdById);
                if (result == null)
                {
                    return BadRequest("用户名或邮箱已存在");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册时发生错误");
                return StatusCode(500, "注册失败，请稍后重试");
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<UserDto>> GetCurrentUser()
        {
            try
            {
                // 调试信息：记录所有Claims
                _logger.LogInformation("当前用户Claims:");
                foreach (var claim in User.Claims)
                {
                    _logger.LogInformation("  {Type}: {Value}", claim.Type, claim.Value);
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                _logger.LogInformation("用户ID: {UserId}, 角色: {Role}", userId, userRole);

                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var user = await _authService.GetUserByIdAsync(userId);
                if (user == null)
                {
                    return NotFound("用户不存在");
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息时发生错误");
                return StatusCode(500, "获取用户信息失败");
            }
        }

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="changePasswordRequest">修改密码请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<ActionResult> ChangePassword([FromBody] ChangePasswordRequestDto changePasswordRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var result = await _authService.ChangePasswordAsync(userId, changePasswordRequest);
                if (!result)
                {
                    return BadRequest("当前密码错误");
                }

                return Ok("密码修改成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密码时发生错误");
                return StatusCode(500, "修改密码失败");
            }
        }

        /// <summary>
        /// 获取用户列表（仅管理员）
        /// </summary>
        /// <returns>用户列表</returns>
        [HttpGet("users")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<UserDto>>> GetUsers()
        {
            try
            {
                // 调试信息：记录用户认证状态
                _logger.LogInformation("获取用户列表请求 - 用户认证状态: {IsAuthenticated}", User.Identity?.IsAuthenticated);
                _logger.LogInformation("用户角色: {Role}", User.FindFirst(ClaimTypes.Role)?.Value);
                _logger.LogInformation("用户ID: {UserId}", User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
                _logger.LogInformation("用户名: {Username}", User.FindFirst(ClaimTypes.Name)?.Value);

                var operatorId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(operatorId))
                {
                    _logger.LogWarning("获取用户列表失败：无法获取操作者ID");
                    return Unauthorized();
                }

                var users = await _authService.GetUsersAsync(operatorId);
                _logger.LogInformation("成功获取用户列表，共 {Count} 个用户", users.Count);
                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表时发生错误");
                return StatusCode(500, "获取用户列表失败");
            }
        }

        /// <summary>
        /// 更新用户权限（仅管理员）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="permissionsRequest">权限请求</param>
        /// <returns>操作结果</returns>
        [HttpPut("users/{userId}/permissions")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> UpdateUserPermissions(string userId, [FromBody] UpdateUserPermissionsRequestDto permissionsRequest)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var operatorId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(operatorId))
                {
                    return Unauthorized();
                }

                var result = await _authService.UpdateUserPermissionsAsync(userId, permissionsRequest, operatorId);
                if (!result)
                {
                    return BadRequest("更新权限失败，用户不存在或无权限操作");
                }

                return Ok("权限更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户权限时发生错误");
                return StatusCode(500, "更新权限失败");
            }
        }

        /// <summary>
        /// 删除用户（仅管理员）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("users/{userId}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> DeleteUser(string userId)
        {
            try
            {
                var operatorId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(operatorId))
                {
                    return Unauthorized();
                }

                var result = await _authService.DeleteUserAsync(userId, operatorId);
                if (!result)
                {
                    return BadRequest("删除用户失败，用户不存在或无权限操作");
                }

                return Ok("用户删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户时发生错误");
                return StatusCode(500, "删除用户失败");
            }
        }

        /// <summary>
        /// 登出（客户端处理，服务端记录日志）
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("logout")]
        [Authorize]
        public ActionResult Logout()
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value;
                _logger.LogInformation("用户 {Username} 登出", username);
                
                return Ok("登出成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登出时发生错误");
                return StatusCode(500, "登出失败");
            }
        }
    }
}
